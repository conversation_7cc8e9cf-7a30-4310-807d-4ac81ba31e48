
import { useState } from 'react';
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import Login from '@/components/Login';
import Dashboard from '@/components/Dashboard';

// Import new MVI pages
import {
  DashboardPage,
  UserManagementPage,
  AnalyticsPage,
  ContentPage,
  SettingsPage
} from '@/interface/pages';

import { ErrorBoundary } from '@/interface/components/common';

const Index = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [useMVI, setUseMVI] = useState(true); // Default to MVI architecture

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  if (!isLoggedIn) {
    return <Login onLogin={handleLogin} />;
  }

  return (
    <ErrorBoundary>
      {/* Toggle button for testing */}
      <div className="fixed top-4 right-4 z-50">
        <button
          onClick={() => setUseMVI(!useMVI)}
          className="bg-blue-600 text-white px-3 py-1 rounded text-sm shadow-lg"
        >
          {useMVI ? 'Old UI' : 'MVI UI'}
        </button>
      </div>

      {/* Render based on architecture choice */}
      {useMVI ? (
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<DashboardPage onLogout={handleLogout} />} />
            <Route path="/users" element={<UserManagementPage onLogout={handleLogout} />} />
            <Route path="/analytics" element={<AnalyticsPage onLogout={handleLogout} />} />
            <Route path="/content" element={<ContentPage onLogout={handleLogout} />} />
            <Route path="/settings" element={<SettingsPage onLogout={handleLogout} />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </BrowserRouter>
      ) : (
        <Dashboard onLogout={handleLogout} />
      )}
    </ErrorBoundary>
  );
};

export default Index;
