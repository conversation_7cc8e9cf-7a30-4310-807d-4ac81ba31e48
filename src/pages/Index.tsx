
import { useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Login from '@/interface/components/features/Login/Login';

// Import MVI pages
import {
  DashboardPage,
  UserManagementPage,
  AnalyticsPage,
  ContentPage,
  SettingsPage
} from '@/interface/pages';

const Index = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleLogin = () => {
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
  };

  if (!isLoggedIn) {
    return <Login onLogin={handleLogin} />;
  }

  return (
    <Routes>
      <Route path="/" element={<DashboardPage onLogout={handleLogout} />} />
      <Route path="/users" element={<UserManagementPage onLogout={handleLogout} />} />
      <Route path="/analytics" element={<AnalyticsPage onLogout={handleLogout} />} />
      <Route path="/content" element={<ContentPage onLogout={handleLogout} />} />
      <Route path="/settings" element={<SettingsPage onLogout={handleLogout} />} />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default Index;
