/**
 * Content Management Page Container - Content management page
 * New page for content management features
 */

import React from 'react';
import { ContentManagementMVI } from '@/interface/components/features/ContentManagement';
import { MainLayout } from '@/interface/components/layout/MainLayout';

interface ContentPageProps {
  onLogout: () => void;
}

export const ContentPage: React.FC<ContentPageProps> = ({ onLogout }) => {
  return (
    <MainLayout onLogout={onLogout}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Quản lý nội dung</h1>
        </div>
        
        <ContentManagementMVI />
      </div>
    </MainLayout>
  );
};

export default ContentPage;
