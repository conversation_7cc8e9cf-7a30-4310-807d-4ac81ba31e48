/**
 * Content Management Page Container - Content management page
 * New page for content management features
 */

import React from 'react';

interface ContentPageProps {
  onLogout: () => void;
}

export const ContentPage: React.FC<ContentPageProps> = ({ onLogout }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Quản lý nội dung</h1>
          <button
            onClick={onLogout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Đăng xuất
          </button>
        </div>
      </header>

      <main className="p-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Content Management MVI</h2>
          <p className="text-gray-700">
            Trang quản lý nội dung với kiến trúc MVI đang được phát triển...
          </p>
        </div>
      </main>
    </div>
  );
};

export default ContentPage;
