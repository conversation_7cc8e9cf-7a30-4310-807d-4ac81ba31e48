/**
 * Dashboard Page Container - Main dashboard orchestration với stats và charts
 * Replaces existing Dashboard.tsx with MVI architecture
 */

import React from 'react';
import { Link } from 'react-router-dom';

interface DashboardPageProps {
  onLogout: () => void;
}

export const DashboardPage: React.FC<DashboardPageProps> = ({ onLogout }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard MVI</h1>
          <button
            onClick={onLogout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Đăng xuất
          </button>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 p-4">
        <div className="flex space-x-4">
          <Link to="/" className="text-blue-600 hover:text-blue-800 font-medium">Dashboard</Link>
          <Link to="/users" className="text-gray-600 hover:text-gray-800">Người dùng</Link>
          <Link to="/analytics" className="text-gray-600 hover:text-gray-800">Phân tích</Link>
          <Link to="/content" className="text-gray-600 hover:text-gray-800">Nội dung</Link>
          <Link to="/settings" className="text-gray-600 hover:text-gray-800">Cài đặt</Link>
        </div>
      </nav>

      {/* Main Content */}
      <main className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Tổng người dùng</h3>
            <p className="text-2xl font-bold text-gray-900">1,250</p>
            <p className="text-sm text-green-600">+12% từ tháng trước</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Người dùng hoạt động</h3>
            <p className="text-2xl font-bold text-gray-900">892</p>
            <p className="text-sm text-green-600">+8% từ tháng trước</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Credits sử dụng</h3>
            <p className="text-2xl font-bold text-gray-900">45K</p>
            <p className="text-sm text-green-600">+15% từ tháng trước</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-sm font-medium text-gray-500">Nội dung tạo</h3>
            <p className="text-2xl font-bold text-gray-900">8K</p>
            <p className="text-sm text-green-600">+23% từ tháng trước</p>
          </div>
        </div>

        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-bold text-gray-900 mb-4">MVI Architecture</h2>
          <p className="text-gray-700">
            Dashboard này sử dụng kiến trúc 3 lớp MVI (Model-View-Interface) với:
          </p>
          <ul className="mt-4 space-y-2 text-gray-600">
            <li>• Model Layer: Types và constants</li>
            <li>• Data Layer: Repositories và data sources</li>
            <li>• Interface Layer: Components, hooks và pages</li>
          </ul>
        </div>
      </main>
    </div>
  );
};

export default DashboardPage;
