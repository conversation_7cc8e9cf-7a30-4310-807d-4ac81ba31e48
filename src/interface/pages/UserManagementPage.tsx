/**
 * User Management Page Container - User management page container với tabs
 * Replaces existing UserCreditManagement.tsx with MVI architecture
 */

import React from 'react';
import { Link } from 'react-router-dom';

interface UserManagementPageProps {
  onLogout: () => void;
}

export const UserManagementPage: React.FC<UserManagementPageProps> = ({ onLogout }) => {
  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Quản lý người dùng</h1>
          <button
            onClick={onLogout}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Đăng xuất
          </button>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200 p-4">
        <div className="flex space-x-4">
          <Link to="/" className="text-gray-600 hover:text-gray-800">Dashboard</Link>
          <Link to="/users" className="text-blue-600 hover:text-blue-800 font-medium">Người dùng</Link>
          <Link to="/analytics" className="text-gray-600 hover:text-gray-800">Phân tích</Link>
          <Link to="/content" className="text-gray-600 hover:text-gray-800">Nội dung</Link>
          <Link to="/settings" className="text-gray-600 hover:text-gray-800">Cài đặt</Link>
        </div>
      </nav>

      <main className="p-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-bold text-gray-900 mb-4">User Management MVI</h2>
          <p className="text-gray-700">
            Trang quản lý người dùng với kiến trúc MVI đang được phát triển...
          </p>
        </div>
      </main>
    </div>
  );
};

export default UserManagementPage;
