/**
 * Enhanced Stats Card MVI - Stats card với data từ MVI repositories
 * Replaces hardcoded data with repository data while keeping same UI
 */

import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Card } from '@/interface/components/ui/card';
import { useAnalyticsPage } from '@/interface/hooks/data';

interface EnhancedStatsCardMVIProps {
  title: string;
  dataKey: 'totalUsers' | 'totalCredits' | 'totalVideos' | 'totalPhotos';
  icon: LucideIcon;
  gradient: string;
  subtitle?: string;
  trend?: number[];
}

export const EnhancedStatsCardMVI: React.FC<EnhancedStatsCardMVIProps> = ({
  title,
  dataKey,
  icon: Icon,
  gradient,
  subtitle,
  trend = []
}) => {
  // Use MVI data hook
  const { metrics, isLoading } = useAnalyticsPage();
  
  // Get value from analytics data
  const getValue = () => {
    if (isLoading || !metrics) return '0';
    
    const value = metrics[dataKey];
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    return '0';
  };

  // Calculate change percentage (mock for now)
  const getChange = () => {
    if (isLoading || !metrics) return '+0%';
    
    // TODO: Implement real change calculation based on historical data
    const mockChanges = {
      totalUsers: '+12.5%',
      totalCredits: '+23.5%',
      totalVideos: '+18.2%',
      totalPhotos: '+15.8%'
    };
    
    return mockChanges[dataKey] || '+0%';
  };

  if (isLoading) {
    return (
      <Card className="p-6 relative overflow-hidden">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/3"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 relative overflow-hidden group hover:shadow-lg transition-all duration-300">
      {/* Background Gradient */}
      <div className={`absolute inset-0 ${gradient} opacity-5 group-hover:opacity-10 transition-opacity duration-300`} />
      
      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-xl ${gradient} bg-opacity-10`}>
            <Icon className="w-6 h-6 text-white" />
          </div>
          
          {/* Trend Chart */}
          {trend.length > 0 && (
            <div className="flex items-end space-x-1 h-8">
              {trend.map((value, index) => (
                <div
                  key={index}
                  className={`w-1 ${gradient} rounded-full opacity-60`}
                  style={{ height: `${(value / Math.max(...trend)) * 100}%` }}
                />
              ))}
            </div>
          )}
        </div>
        
        <div className="space-y-1">
          <p className="text-sm font-medium text-slate-600">{title}</p>
          <p className="text-2xl font-bold text-slate-900">{getValue()}</p>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-green-600 font-medium">
              {getChange()}
            </span>
            {subtitle && (
              <span className="text-xs text-slate-500">{subtitle}</span>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};
