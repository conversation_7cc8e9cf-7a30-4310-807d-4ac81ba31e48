/**
 * User Growth Chart MVI - Chart component với data từ MVI repositories
 * Replaces hardcoded chart data with repository data while keeping same UI
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/interface/components/ui/card';
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { useAnalyticsPage } from '@/interface/hooks/data';

export const UserGrowthChartMVI: React.FC = () => {
  // Use MVI data hook
  const { growthChart, metrics, isLoading } = useAnalyticsPage();

  // Get chart data from analytics - format for Recharts
  const getChartData = () => {
    if (isLoading || !growthChart?.labels) {
      // Return mock data matching original dashboard format
      return [
        { name: 'Jan', users: 1200, videos: 450 },
        { name: 'Feb', users: 1890, videos: 680 },
        { name: 'Mar', users: 2450, videos: 920 },
        { name: 'Apr', users: 3200, videos: 1240 },
        { name: 'May', users: 4100, videos: 1680 },
        { name: 'Jun', users: 5200, videos: 2150 },
        { name: 'Jul', users: 6800, videos: 2890 }
      ];
    }

    // Convert GrowthChart format to Recharts format
    const chartData = growthChart.labels.map((label, index) => ({
      name: label,
      users: growthChart.datasets[0]?.data[index] || 0,
      videos: growthChart.datasets[1]?.data[index] || 0
    }));

    return chartData;
  };

  const chartData = getChartData();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <Card className="p-6 animate-fade-in">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Tăng trưởng người dùng</h3>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={chartData}>
            <defs>
              <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis dataKey="name" stroke="#64748b" />
            <YAxis stroke="#64748b" />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
            />
            <Area type="monotone" dataKey="users" stroke="#10b981" fillOpacity={1} fill="url(#colorUsers)" />
          </AreaChart>
        </ResponsiveContainer>
      </Card>

      <Card className="p-6 animate-fade-in">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Video đã được tạo</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
            <XAxis dataKey="name" stroke="#64748b" />
            <YAxis stroke="#64748b" />
            <Tooltip
              contentStyle={{
                backgroundColor: 'white',
                border: '1px solid #e2e8f0',
                borderRadius: '8px',
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
              }}
            />
            <Bar dataKey="videos" fill="#6366f1" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </Card>
    </div>
  );
};
