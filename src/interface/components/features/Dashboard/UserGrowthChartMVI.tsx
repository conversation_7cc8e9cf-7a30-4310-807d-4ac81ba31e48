/**
 * User Growth Chart MVI - Chart component với data từ MVI repositories
 * Replaces hardcoded chart data with repository data while keeping same UI
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/interface/components/ui/card';
import { useAnalyticsPage } from '@/interface/hooks/data';

export const UserGrowthChartMVI: React.FC = () => {
  // Use MVI data hook
  const { growthChart, metrics, isLoading } = useAnalyticsPage();

  // Get chart data from analytics
  const getChartData = () => {
    if (isLoading || !growthChart?.labels) {
      // Return mock data while loading
      return [
        { month: 'Jan', users: 800, premium: 150 },
        { month: 'Feb', users: 850, premium: 170 },
        { month: 'Mar', users: 900, premium: 180 },
        { month: 'Apr', users: 950, premium: 190 },
        { month: 'May', users: 1000, premium: 200 }
      ];
    }

    // Convert GrowthChart format to our display format
    const chartData = growthChart.labels.map((label, index) => ({
      month: label,
      users: growthChart.datasets[0]?.data[index] || 0,
      premium: growthChart.datasets[1]?.data[index] || 0
    }));

    return chartData;
  };

  const chartData = getChartData();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse">
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tăng trưởng người dùng</CardTitle>
        <CardDescription>
          Thống kê người dùng theo tháng (Data từ MVI Repository)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Simple bar chart representation */}
          <div className="grid grid-cols-5 gap-4 h-64">
            {chartData.map((data, index) => {
              const maxUsers = Math.max(...chartData.map(d => d.users));
              const userHeight = (data.users / maxUsers) * 100;
              const premiumHeight = (data.premium / maxUsers) * 100;
              
              return (
                <div key={index} className="flex flex-col items-center justify-end h-full">
                  <div className="w-full flex flex-col justify-end h-full space-y-1">
                    {/* Premium users bar */}
                    <div 
                      className="w-full bg-blue-500 rounded-t"
                      style={{ height: `${premiumHeight}%` }}
                      title={`Premium: ${data.premium}`}
                    />
                    {/* Regular users bar */}
                    <div 
                      className="w-full bg-blue-200 rounded-b"
                      style={{ height: `${userHeight - premiumHeight}%` }}
                      title={`Total: ${data.users}`}
                    />
                  </div>
                  <span className="text-xs text-gray-600 mt-2">{data.month}</span>
                </div>
              );
            })}
          </div>
          
          {/* Legend */}
          <div className="flex items-center justify-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-200 rounded"></div>
              <span>Người dùng thường</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>Người dùng premium</span>
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {metrics?.totalUsers?.toLocaleString() || '0'}
              </p>
              <p className="text-sm text-gray-600">Tổng người dùng</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {metrics?.premiumUsers?.toLocaleString() || '0'}
              </p>
              <p className="text-sm text-gray-600">Người dùng premium</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
