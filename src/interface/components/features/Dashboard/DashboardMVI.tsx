/**
 * DashboardMVI - Direct replacement for Dashboard.tsx
 * Preserves EXACT same UI/UX but uses 3-layer MVI architecture
 * Data now comes from repository layer instead of hardcoded statsData
 */

import { useState } from 'react';
import {
  Search,
  Bell,
  User
} from 'lucide-react';
import Sidebar from '@/components/Sidebar';
import UserGrowthChart from '@/components/UserGrowthChart';
import NotificationManagement from '@/components/NotificationManagement';
import SystemSettings from '@/components/SystemSettings';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

// Import the new MVI components
import UserCreditManagementMVI from '../UserManagement/UserCreditManagementMVI';
import AnalyticsMVI from '../Analytics/AnalyticsMVI';
import ContentManagementMVI from '../ContentManagement/ContentManagementMVI';

interface DashboardProps {
  onLogout: () => void;
}

const DashboardMVI = ({ onLogout }: DashboardProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const getSectionTitle = (section: string) => {
    switch (section) {
      case 'dashboard': return 'Bảng điều khiển';
      case 'users': return 'Quản lý người dùng & Credits';
      case 'content': return 'Quản lý nội dung';
      case 'analytics': return 'Báo cáo & Phân tích';
      case 'notifications': return 'Quản lý thông báo';
      case 'settings': return 'Cài đặt hệ thống';
      default: return section;
    }
  };

  const getSectionDescription = (section: string) => {
    switch (section) {
      case 'dashboard': return 'Tổng quan platform SaaS cho nội dung cá nhân';
      case 'users': return 'Quản lý người dùng, credits và thống kê chi tiết';
      case 'content': return 'Quản lý videos và photos được tạo';
      case 'analytics': return 'Thống kê và phân tích hiệu suất';
      case 'notifications': return 'Gửi và quản lý thông báo';
      case 'settings': return 'Cấu hình và thiết lập hệ thống';
      default: return 'Quản lý platform tạo video và photo cá nhân bằng credits';
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'users':
        // Use new MVI component instead of old UserCreditManagement
        return <UserCreditManagementMVI />;
      case 'content':
        return <ContentManagementMVI />;
      case 'analytics':
        return <AnalyticsMVI />;
      case 'notifications':
        return <NotificationManagement />;
      case 'settings':
        return <SystemSettings />;
      default:
        return (
          <div className="spacing-responsive">
            {/* Temporary simple dashboard */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Tổng người dùng</h3>
                <p className="text-2xl font-bold text-gray-900">1,250</p>
                <p className="text-sm text-green-600">+12% từ tháng trước</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Người dùng hoạt động</h3>
                <p className="text-2xl font-bold text-gray-900">892</p>
                <p className="text-sm text-green-600">+8% từ tháng trước</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Credits sử dụng</h3>
                <p className="text-2xl font-bold text-gray-900">45K</p>
                <p className="text-sm text-green-600">+15% từ tháng trước</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow">
                <h3 className="text-sm font-medium text-gray-500">Nội dung tạo</h3>
                <p className="text-2xl font-bold text-gray-900">8K</p>
                <p className="text-sm text-green-600">+23% từ tháng trước</p>
              </div>
            </div>

            {/* Charts */}
            <UserGrowthChart />
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen bg-slate-50">
      <Sidebar 
        activeSection={activeSection} 
        onSectionChange={setActiveSection}
        onLogout={onLogout}
      />
      
      <div className="flex-1 overflow-auto w-full min-w-0">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-slate-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl font-bold text-slate-900 truncate">
                {getSectionTitle(activeSection)}
              </h1>
              <p className="text-sm sm:text-base text-slate-600 mt-1">
                {getSectionDescription(activeSection)}
              </p>
            </div>
            
            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <div className="relative hidden sm:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input 
                  placeholder="Tìm kiếm..." 
                  className="pl-10 w-60 lg:w-80 bg-slate-50 border-slate-200 focus:bg-white"
                />
              </div>
              
              <Button variant="ghost" size="icon" className="relative flex-shrink-0">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </Button>
              
              <Button variant="ghost" size="icon" className="flex-shrink-0">
                <User className="w-5 h-5" />
              </Button>
            </div>
          </div>
          
          {/* Mobile Search */}
          <div className="relative mt-4 sm:hidden">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <Input 
              placeholder="Tìm kiếm..." 
              className="pl-10 w-full bg-slate-50 border-slate-200 focus:bg-white"
            />
          </div>
        </header>

        {/* Main Content */}
        <main className="p-4 sm:p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default DashboardMVI;
