/**
 * UserTable - User table component with filtering and pagination
 */

import React from 'react';
import { Card } from '@/interface/components/ui/card';
import { Button } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { 
  Search, 
  Filter, 
  Mail, 
  Video,
  Image,
  CreditCard,
  BarChart3
} from 'lucide-react';

import { User } from '@/models/types';
import { formatNumber } from '@/interface/utils/formatters';

interface UserTableProps {
  users: User[];
  isLoading: boolean;
  pagination: any;
  filters: any;
  selectedUserDetails: number | null;
  onUserDetailsToggle: (userId: number) => void;
  onManageCredits: (userId: number, userName: string) => void;
  userHelpers: any;
}

export const UserTable: React.FC<UserTableProps> = ({
  users,
  isLoading,
  pagination: _pagination,
  filters,
  selectedUserDetails,
  onUserDetailsToggle,
  onManageCredits,
  userHelpers
}) => {
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    filters.updateFilter('searchTerm', e.target.value);
  };

  const renderUserDetails = (user: User) => {
    const successRate = userHelpers.calculateSuccessRate(user);

    return (
      <div className="mt-6 p-4 sm:p-6 bg-slate-50 rounded-lg">
        <h4 className="text-lg font-semibold mb-4">Chi tiết tháng này - {user.name}</h4>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Video className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Videos hoàn thành</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.videosCompleted}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <Video className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Videos lỗi</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.videosErrors}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Image className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Photos hoàn thành</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.photosCompleted}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <Image className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Photos lỗi</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.photosErrors}</p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-4">
            <h5 className="font-semibold mb-3">Tỷ lệ thành công</h5>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Videos</span>
                  <span className="text-sm font-medium">{successRate.video}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${successRate.video}%` }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Photos</span>
                  <span className="text-sm font-medium">{successRate.photo}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${successRate.photo}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h5 className="font-semibold mb-3">Credits tháng này</h5>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Đã sử dụng</span>
                <span className="font-medium text-red-600">{formatNumber(user.monthlyStats.creditsUsed)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Đã thêm</span>
                <span className="font-medium text-green-600">{formatNumber(user.monthlyStats.creditsAdded)}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t">
                <span className="text-sm font-medium">Chênh lệch</span>
                <span className={`font-bold ${userHelpers.calculateMonthlyCreditDifference(user) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {userHelpers.calculateMonthlyCreditDifference(user) > 0 ? '+' : ''}{formatNumber(userHelpers.calculateMonthlyCreditDifference(user))}
                </span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className="p-4 sm:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
          <Input
            placeholder="Tìm kiếm theo tên hoặc email..."
            value={filters.filters.searchTerm || ''}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>
        <Button variant="outline" className="flex-shrink-0">
          <Filter className="w-4 h-4 mr-2" />
          Lọc
        </Button>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full min-w-[800px]">
          <thead>
            <tr className="border-b border-slate-200">
              <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Người dùng</th>
              <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Credits</th>
              <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Nội dung</th>
              <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Trạng thái</th>
              <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Thao tác</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <React.Fragment key={user.id}>
                <tr className="border-b border-slate-100 hover:bg-slate-50">
                  <td className="py-4 px-2 sm:px-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-medium text-xs sm:text-sm">
                          {userHelpers.getUserInitials(user.name)}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-medium text-slate-900 truncate">{user.name}</p>
                        <div className="flex items-center text-xs sm:text-sm text-slate-500">
                          <Mail className="w-3 h-3 mr-1 flex-shrink-0" />
                          <span className="truncate">{user.email}</span>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <Badge className="bg-blue-100 text-blue-800 text-xs">
                      {formatNumber(user.credits)}
                    </Badge>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <div className="space-y-1">
                      <div className="flex items-center text-xs sm:text-sm">
                        <Video className="w-3 h-3 mr-1 text-blue-600 flex-shrink-0" />
                        <span>{user.videosCreated}</span>
                      </div>
                      <div className="flex items-center text-xs sm:text-sm">
                        <Image className="w-3 h-3 mr-1 text-green-600 flex-shrink-0" />
                        <span>{user.photosCreated}</span>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <Badge className={userHelpers.getStatusInfo(user.status).color}>
                      {userHelpers.getStatusInfo(user.status).label}
                    </Badge>
                  </td>
                  <td className="py-4 px-2 sm:px-4">
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onManageCredits(user.id, user.name)}
                        className="text-xs px-2"
                      >
                        <CreditCard className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Credits</span>
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onUserDetailsToggle(user.id)}
                        className="px-2"
                      >
                        <BarChart3 className="w-3 h-3" />
                      </Button>
                    </div>
                  </td>
                </tr>
                
                {/* User Details */}
                {selectedUserDetails === user.id && renderUserDetails(user)}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </Card>
  );
};
