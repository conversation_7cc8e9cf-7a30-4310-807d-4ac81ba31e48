/**
 * NotificationManagementMVI - Direct replacement for NotificationManagement.tsx
 * Preserves EXACT same UI/UX but uses 3-layer MVI architecture
 * Data now comes from repository layer instead of hardcoded
 */

import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { But<PERSON> } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import {
  Bell,
  Search,
  Filter,
  MoreHorizontal,
  Send,
  Users,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Info,
  X
} from 'lucide-react';

// Data hooks - Repository integration (replaces hardcoded notificationData)
// import { useNotifications } from '@/interface/hooks/data';
// import { NOTIFICATION_STATUS_COLORS, NOTIFICATION_TYPE_LABELS } from '@/models/constants';

// Mock constants for now
const NOTIFICATION_STATUS_COLORS: Record<string, string> = {
  sent: 'bg-green-100 text-green-800',
  pending: 'bg-yellow-100 text-yellow-800',
  failed: 'bg-red-100 text-red-800',
  info: 'bg-blue-100 text-blue-800',
  warning: 'bg-orange-100 text-orange-800',
  success: 'bg-green-100 text-green-800',
  error: 'bg-red-100 text-red-800'
};

const NOTIFICATION_TYPE_LABELS: Record<string, string> = {
  sent: 'Đã gửi',
  pending: 'Chờ gửi',
  failed: 'Thất bại',
  info: 'Thông tin',
  warning: 'Cảnh báo',
  success: 'Thành công',
  error: 'Lỗi'
};

export const NotificationManagementMVI: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Use MVI data hook instead of hardcoded data
  // TODO: Implement useNotifications hook
  const notifications = [
    {
      id: '1',
      title: 'Thông báo bảo trì hệ thống',
      message: 'Hệ thống sẽ được bảo trì vào 2:00 AM ngày mai',
      type: 'warning',
      status: 'sent',
      recipients: 1250,
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      title: 'Cập nhật tính năng mới',
      message: 'Chúng tôi đã thêm tính năng tạo video AI mới',
      type: 'info',
      status: 'sent',
      recipients: 2100,
      createdAt: new Date().toISOString()
    }
  ];
  const isLoading = false;
  const error = null;
  const refetch = () => {};

  // Filter notifications based on search and filters
  const filteredNotifications = notifications?.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || notification.type === selectedType;
    const matchesStatus = selectedStatus === 'all' || notification.status === selectedStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  }) || [];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'info': return <Info className="w-4 h-4" />;
      case 'warning': return <AlertTriangle className="w-4 h-4" />;
      case 'success': return <CheckCircle className="w-4 h-4" />;
      case 'error': return <X className="w-4 h-4" />;
      default: return <Bell className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    return NOTIFICATION_STATUS_COLORS[status] || 'bg-gray-100 text-gray-800';
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Lỗi tải thông báo: {error.message}</p>
        <Button onClick={refetch} className="mt-4">Thử lại</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Quản lý thông báo</h2>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Send className="w-4 h-4 mr-2" />
          Gửi thông báo mới
        </Button>
      </div>

      {/* Filters */}
      <Card className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Tìm kiếm thông báo..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tất cả loại</option>
            <option value="info">Thông tin</option>
            <option value="warning">Cảnh báo</option>
            <option value="success">Thành công</option>
            <option value="error">Lỗi</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tất cả trạng thái</option>
            <option value="sent">Đã gửi</option>
            <option value="pending">Chờ gửi</option>
            <option value="failed">Thất bại</option>
          </select>

          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Lọc
          </Button>
        </div>
      </Card>

      {/* Notifications List */}
      <div className="space-y-4">
        {filteredNotifications.map((notification) => (
          <Card key={notification.id} className="p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4 flex-1">
                <div className={`p-2 rounded-lg ${getStatusColor(notification.type)}`}>
                  {getTypeIcon(notification.type)}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-semibold text-gray-900">{notification.title}</h3>
                    <Badge className={getStatusColor(notification.status)}>
                      {NOTIFICATION_TYPE_LABELS[notification.status] || notification.status}
                    </Badge>
                  </div>
                  
                  <p className="text-gray-600 mb-3">{notification.message}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{notification.recipients} người nhận</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(notification.createdAt).toLocaleDateString('vi-VN')}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {filteredNotifications.length === 0 && (
        <div className="text-center py-8">
          <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">Không có thông báo nào</p>
        </div>
      )}
    </div>
  );
};
