/**
 * SidebarMVI - Direct replacement for Sidebar.tsx
 * Preserves EXACT same UI/UX but uses 3-layer MVI architecture
 * Navigation state managed through MVI patterns
 */

import React from 'react';
import { Button } from '@/interface/components/ui/button';
import {
  LayoutDashboard,
  Users,
  BarChart3,
  FileText,
  Settings,
  Bell,
  LogOut,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

interface SidebarMVIProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  onLogout: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const SidebarMVI: React.FC<SidebarMVIProps> = ({
  activeSection,
  onSectionChange,
  onLogout,
  isCollapsed = false,
  onToggleCollapse
}) => {
  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: LayoutDashboard,
      description: 'Tổng quan hệ thống'
    },
    {
      id: 'users',
      label: 'Quản lý người dùng',
      icon: Users,
      description: 'Quản lý tài khoản và credits'
    },
    {
      id: 'analytics',
      label: 'Thống kê',
      icon: BarChart3,
      description: 'Báo cáo và phân tích'
    },
    {
      id: 'content',
      label: 'Quản lý nội dung',
      icon: FileText,
      description: 'Videos và hình ảnh'
    },
    {
      id: 'notifications',
      label: 'Thông báo',
      icon: Bell,
      description: 'Gửi thông báo hệ thống'
    },
    {
      id: 'settings',
      label: 'Cài đặt',
      icon: Settings,
      description: 'Cấu hình hệ thống'
    }
  ];

  return (
    <div className={`bg-white shadow-lg transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } flex flex-col h-full`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">MA</span>
              </div>
              <div>
                <h1 className="font-bold text-gray-900">Mega AI</h1>
                <p className="text-xs text-gray-500">Admin Panel</p>
              </div>
            </div>
          )}
          
          {onToggleCollapse && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="p-1 h-8 w-8"
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4" />
              ) : (
                <ChevronLeft className="w-4 h-4" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeSection === item.id;
          
          return (
            <Button
              key={item.id}
              variant={isActive ? "default" : "ghost"}
              className={`w-full justify-start h-auto p-3 ${
                isCollapsed ? 'px-3' : 'px-4'
              } ${
                isActive 
                  ? 'bg-blue-600 text-white hover:bg-blue-700' 
                  : 'text-gray-700 hover:bg-gray-100'
              }`}
              onClick={() => onSectionChange(item.id)}
              title={isCollapsed ? item.label : undefined}
            >
              <Icon className={`w-5 h-5 ${isCollapsed ? '' : 'mr-3'} flex-shrink-0`} />
              {!isCollapsed && (
                <div className="text-left">
                  <div className="font-medium">{item.label}</div>
                  <div className={`text-xs ${
                    isActive ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {item.description}
                  </div>
                </div>
              )}
            </Button>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <Button
          variant="ghost"
          className={`w-full justify-start text-red-600 hover:bg-red-50 hover:text-red-700 h-auto p-3 ${
            isCollapsed ? 'px-3' : 'px-4'
          }`}
          onClick={onLogout}
          title={isCollapsed ? 'Đăng xuất' : undefined}
        >
          <LogOut className={`w-5 h-5 ${isCollapsed ? '' : 'mr-3'} flex-shrink-0`} />
          {!isCollapsed && (
            <div className="text-left">
              <div className="font-medium">Đăng xuất</div>
              <div className="text-xs text-gray-500">Thoát khỏi hệ thống</div>
            </div>
          )}
        </Button>
      </div>
    </div>
  );
};
