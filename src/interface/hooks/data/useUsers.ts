/**
 * useUsers Hook - User data management với React Query + userRepository + Zod validation
 * Replaces direct data access from UserCreditManagement.tsx
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userRepository } from '@/data/repositories';
import { QUERY_KEYS, QUERY_STALE_TIME, QUERY_CACHE_TIME } from '@/data/constants';
import {
  validateCreateUser,
  validateUpdateUser,
  validateUserFilters,
  validatePagination,
  type CreateUserFormData,
  type UpdateUserFormData,
  type UserFiltersFormData,
  type PaginationFormData
} from '@/interface/utils/validation';
import {
  User,
  UserFilters,
  UserSummary,
  PaginatedResponse,
  PaginationParams
} from '@/models/types';
import { toast } from '@/hooks/use-toast';

/**
 * Hook to get paginated users list with filtering
 */
export const useUsers = (
  params: PaginationFormData,
  filters?: UserFiltersFormData
) => {
  return useQuery({
    queryKey: QUERY_KEYS.USERS.LIST(filters),
    queryFn: async (): Promise<PaginatedResponse<User>> => {
      // Validate input parameters
      const validatedParams = validatePagination(params);
      const validatedFilters = filters ? validateUserFilters(filters) : undefined;

      // Convert to repository format
      const repositoryParams: PaginationParams = {
        page: validatedParams.page || 1,
        pageSize: validatedParams.pageSize || 10,
        sortBy: validatedParams.sortBy,
        sortOrder: validatedParams.sortOrder
      };

      // Convert filters to repository format
      const repositoryFilters: UserFilters | undefined = validatedFilters ? {
        status: validatedFilters.status,
        searchTerm: validatedFilters.searchTerm,
        dateRange: validatedFilters.dateRange && validatedFilters.dateRange.from && validatedFilters.dateRange.to ? {
          from: validatedFilters.dateRange.from,
          to: validatedFilters.dateRange.to
        } : undefined
      } : undefined;

      const response = await userRepository.getUsers(repositoryParams, repositoryFilters);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch users');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.MEDIUM,
    gcTime: QUERY_CACHE_TIME.LONG,
    retry: 2
  });
};

/**
 * Hook to get single user by ID
 */
export const useUser = (id: number) => {
  return useQuery({
    queryKey: QUERY_KEYS.USERS.DETAIL(id),
    queryFn: async (): Promise<User | null> => {
      const response = await userRepository.getUserById(id);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch user');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.MEDIUM,
    gcTime: QUERY_CACHE_TIME.MEDIUM,
    enabled: !!id
  });
};

/**
 * Hook to get user summary statistics
 */
export const useUserSummary = () => {
  return useQuery({
    queryKey: QUERY_KEYS.USERS.SUMMARY,
    queryFn: async (): Promise<UserSummary> => {
      const response = await userRepository.getUserSummary();
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch user summary');
      }
      
      return response.data!;
    },
    staleTime: QUERY_STALE_TIME.SHORT,
    gcTime: QUERY_CACHE_TIME.MEDIUM
  });
};

/**
 * Hook to create new user
 */
export const useCreateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userData: CreateUserFormData): Promise<User> => {
      // Validate input data
      const validatedData = validateCreateUser(userData);

      // Convert to repository format (ensure required fields)
      const repositoryData: Omit<User, 'id' | 'monthlyStats' | 'lastActivity' | 'joinDate'> = {
        name: validatedData.name,
        email: validatedData.email,
        phone: validatedData.phone,
        credits: validatedData.credits,
        videosCreated: validatedData.videosCreated,
        photosCreated: validatedData.photosCreated,
        totalUsed: validatedData.totalUsed,
        totalAdded: validatedData.totalAdded,
        status: validatedData.status
      };

      const response = await userRepository.createUser(repositoryData);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to create user');
      }
      
      return response.data!;
    },
    onSuccess: (newUser) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USERS.ALL });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USERS.SUMMARY });
      
      toast({
        title: "Thành công",
        description: `Đã tạo người dùng ${newUser.name}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    }
  });
};

/**
 * Hook to update user
 */
export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, userData }: { id: number; userData: UpdateUserFormData }): Promise<User> => {
      // Validate input data
      const validatedData = validateUpdateUser(userData);

      // Convert to repository format (filter out undefined values)
      const repositoryData: Partial<User> = {};

      if (validatedData.name !== undefined) repositoryData.name = validatedData.name;
      if (validatedData.email !== undefined) repositoryData.email = validatedData.email;
      if (validatedData.phone !== undefined) repositoryData.phone = validatedData.phone;
      if (validatedData.credits !== undefined) repositoryData.credits = validatedData.credits;
      if (validatedData.videosCreated !== undefined) repositoryData.videosCreated = validatedData.videosCreated;
      if (validatedData.photosCreated !== undefined) repositoryData.photosCreated = validatedData.photosCreated;
      if (validatedData.totalUsed !== undefined) repositoryData.totalUsed = validatedData.totalUsed;
      if (validatedData.totalAdded !== undefined) repositoryData.totalAdded = validatedData.totalAdded;
      if (validatedData.status !== undefined) repositoryData.status = validatedData.status;

      const response = await userRepository.updateUser(id, repositoryData);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to update user');
      }
      
      return response.data!;
    },
    onSuccess: (updatedUser) => {
      // Update specific user in cache
      queryClient.setQueryData(
        QUERY_KEYS.USERS.DETAIL(updatedUser.id),
        updatedUser
      );
      
      // Invalidate users list and summary
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USERS.ALL });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USERS.SUMMARY });
      
      toast({
        title: "Thành công",
        description: `Đã cập nhật người dùng ${updatedUser.name}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    }
  });
};

/**
 * Hook to delete user
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: number): Promise<boolean> => {
      const response = await userRepository.deleteUser(id);
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to delete user');
      }
      
      return response.data!;
    },
    onSuccess: (_, deletedUserId) => {
      // Remove user from cache
      queryClient.removeQueries({ queryKey: QUERY_KEYS.USERS.DETAIL(deletedUserId) });
      
      // Invalidate users list and summary
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USERS.ALL });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USERS.SUMMARY });
      
      toast({
        title: "Thành công",
        description: "Đã xóa người dùng",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Lỗi",
        description: error.message,
        variant: "destructive",
      });
    }
  });
};

/**
 * Helper functions from userRepository
 */
export const useUserHelpers = () => {
  return {
    calculateSuccessRate: userRepository.calculateSuccessRate.bind(userRepository),
    getStatusInfo: userRepository.getStatusInfo.bind(userRepository),
    calculateMonthlyCreditDifference: userRepository.calculateMonthlyCreditDifference.bind(userRepository),
    getUserInitials: userRepository.getUserInitials.bind(userRepository)
  };
};
