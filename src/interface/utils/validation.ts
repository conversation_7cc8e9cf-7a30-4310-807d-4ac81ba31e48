/**
 * Zod schemas and validation utilities for Interface Layer
 * NOTE: Zod validators are placed in Interface Utils, NOT in Model Layer
 */

import { z } from 'zod';
import { VALIDATION_RULES } from '@/models/constants';

// User validation schemas
export const userSchema = z.object({
  id: z.number().positive(),
  name: z.string()
    .min(1, 'Tên không được để trống')
    .max(VALIDATION_RULES.MAX_NAME_LENGTH, `Tên không được vượt quá ${VALIDATION_RULES.MAX_NAME_LENGTH} ký tự`),
  email: z.string()
    .email('Email không hợp lệ')
    .max(VALIDATION_RULES.MAX_EMAIL_LENGTH, `Email không được vượt quá ${VALIDATION_RULES.MAX_EMAIL_LENGTH} ký tự`),
  phone: z.string()
    .min(10, '<PERSON><PERSON> điện thoại phải có ít nhất 10 số')
    .max(VALIDATION_RULES.MAX_PHONE_LENGTH, `Số điện thoại không được vượt quá ${VALIDATION_RULES.MAX_PHONE_LENGTH} ký tự`)
    .regex(/^\+?[\d\s-()]+$/, 'Số điện thoại không hợp lệ'),
  credits: z.number()
    .min(VALIDATION_RULES.MIN_CREDITS, `Credits không được nhỏ hơn ${VALIDATION_RULES.MIN_CREDITS}`)
    .max(VALIDATION_RULES.MAX_CREDITS, `Credits không được vượt quá ${VALIDATION_RULES.MAX_CREDITS}`),
  videosCreated: z.number().min(0),
  photosCreated: z.number().min(0),
  totalUsed: z.number().min(0),
  totalAdded: z.number().min(0),
  monthlyStats: z.object({
    videosCompleted: z.number().min(0),
    videosErrors: z.number().min(0),
    photosCompleted: z.number().min(0),
    photosErrors: z.number().min(0),
    creditsUsed: z.number().min(0),
    creditsAdded: z.number().min(0)
  }),
  status: z.enum(['active', 'premium', 'inactive']),
  lastActivity: z.string(),
  joinDate: z.string()
});

export const createUserSchema = userSchema.omit({
  id: true,
  lastActivity: true,
  joinDate: true,
  monthlyStats: true
});

export const updateUserSchema = userSchema.partial().omit({ 
  id: true 
});

// Credit validation schemas
export const creditTransactionSchema = z.object({
  userId: z.number().positive('User ID phải là số dương'),
  amount: z.number()
    .positive('Số lượng credits phải lớn hơn 0')
    .max(VALIDATION_RULES.MAX_CREDITS, `Số lượng credits không được vượt quá ${VALIDATION_RULES.MAX_CREDITS}`),
  description: z.string()
    .min(1, 'Mô tả không được để trống')
    .max(255, 'Mô tả không được vượt quá 255 ký tự'),
  adminId: z.number().positive().optional(),
  adminName: z.string().optional()
});

export const addCreditsSchema = creditTransactionSchema;
export const subtractCreditsSchema = creditTransactionSchema;

// Content validation schemas
export const contentSchema = z.object({
  id: z.string(),
  userId: z.number().positive(),
  type: z.enum(['video', 'photo']),
  title: z.string()
    .min(1, 'Tiêu đề không được để trống')
    .max(100, 'Tiêu đề không được vượt quá 100 ký tự'),
  description: z.string()
    .max(500, 'Mô tả không được vượt quá 500 ký tự')
    .optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']),
  creditsUsed: z.number().min(0),
  metadata: z.object({
    originalPrompt: z.string().optional(),
    style: z.string().optional(),
    quality: z.enum(['low', 'medium', 'high', 'ultra']),
    duration: z.number().positive().optional(),
    dimensions: z.object({
      width: z.number().positive(),
      height: z.number().positive()
    }).optional(),
    tags: z.array(z.string()).optional(),
    isPublic: z.boolean()
  })
});

export const createContentSchema = contentSchema.omit({ 
  id: true, 
  status: true 
});

// Notification validation schemas
export const notificationSchema = z.object({
  title: z.string()
    .min(1, 'Tiêu đề không được để trống')
    .max(100, 'Tiêu đề không được vượt quá 100 ký tự'),
  message: z.string()
    .min(1, 'Nội dung không được để trống')
    .max(1000, 'Nội dung không được vượt quá 1000 ký tự'),
  type: z.enum(['system', 'credit', 'content', 'promotion', 'maintenance', 'security']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  targetUsers: z.object({
    type: z.enum(['all', 'specific', 'filtered']),
    userIds: z.array(z.number().positive()).optional(),
    filters: z.object({
      status: z.array(z.string()).optional(),
      joinDateRange: z.object({
        from: z.string(),
        to: z.string()
      }).optional(),
      creditRange: z.object({
        min: z.number().min(0),
        max: z.number().min(0)
      }).optional()
    }).optional()
  }),
  scheduledAt: z.string().optional(),
  metadata: z.object({
    channels: z.array(z.enum(['in-app', 'email', 'sms', 'push'])),
    template: z.string().optional(),
    variables: z.record(z.any()).optional(),
    actionUrl: z.string().url().optional(),
    actionLabel: z.string().optional(),
    expiresAt: z.string().optional()
  }).optional()
});

// Filter validation schemas
export const userFiltersSchema = z.object({
  status: z.enum(['active', 'premium', 'inactive']).optional(),
  searchTerm: z.string().optional(),
  dateRange: z.object({
    from: z.string(),
    to: z.string()
  }).optional()
});

export const contentFiltersSchema = z.object({
  type: z.enum(['video', 'photo']).optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']).optional(),
  userId: z.number().positive().optional(),
  dateRange: z.object({
    from: z.string(),
    to: z.string()
  }).optional(),
  quality: z.enum(['low', 'medium', 'high', 'ultra']).optional(),
  searchTerm: z.string().optional()
});

// Pagination validation schema
export const paginationSchema = z.object({
  page: z.number().min(1, 'Trang phải lớn hơn 0'),
  pageSize: z.number()
    .min(1, 'Kích thước trang phải lớn hơn 0')
    .max(100, 'Kích thước trang không được vượt quá 100'),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

// Type exports for TypeScript
export type UserFormData = z.infer<typeof userSchema>;
export type CreateUserFormData = z.infer<typeof createUserSchema>;
export type UpdateUserFormData = z.infer<typeof updateUserSchema>;
export type CreditTransactionFormData = z.infer<typeof creditTransactionSchema>;
export type ContentFormData = z.infer<typeof contentSchema>;
export type CreateContentFormData = z.infer<typeof createContentSchema>;
export type NotificationFormData = z.infer<typeof notificationSchema>;
export type UserFiltersFormData = z.infer<typeof userFiltersSchema>;
export type ContentFiltersFormData = z.infer<typeof contentFiltersSchema>;
export type PaginationFormData = z.infer<typeof paginationSchema>;

// Validation helper functions
export const validateUser = (data: unknown) => userSchema.parse(data);
export const validateCreateUser = (data: unknown) => createUserSchema.parse(data);
export const validateUpdateUser = (data: unknown) => updateUserSchema.parse(data);
export const validateCreditTransaction = (data: unknown) => creditTransactionSchema.parse(data);
export const validateContent = (data: unknown) => contentSchema.parse(data);
export const validateCreateContent = (data: unknown) => createContentSchema.parse(data);
export const validateNotification = (data: unknown) => notificationSchema.parse(data);
export const validateUserFilters = (data: unknown) => userFiltersSchema.parse(data);
export const validateContentFilters = (data: unknown) => contentFiltersSchema.parse(data);
export const validatePagination = (data: unknown) => paginationSchema.parse(data);
