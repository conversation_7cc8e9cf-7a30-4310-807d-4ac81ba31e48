/**
 * Testing Utilities
 * Helper functions for testing MVI architecture components
 */

import { QueryClient } from '@tanstack/react-query';
import { User, Analytics, Credit } from '@/models/types';

/**
 * Create test query client with default configuration
 */
export const createTestQueryClient = (): QueryClient => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

/**
 * Mock data generators for testing
 */
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 1,
  name: 'Test User',
  email: '<EMAIL>',
  phone: '0123456789',
  credits: 100,
  videosCreated: 5,
  photosCreated: 10,
  totalUsed: 50,
  totalAdded: 150,
  monthlyStats: {
    videosCompleted: 4,
    videosErrors: 1,
    photosCompleted: 8,
    photosErrors: 2,
    creditsUsed: 30,
    creditsAdded: 50
  },
  lastActivity: '2024-01-15T10:30:00Z',
  joinDate: '2024-01-01',
  status: 'active',
  ...overrides
});

export const createMockUsers = (count: number = 5): User[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockUser({
      id: index + 1,
      name: `User ${index + 1}`,
      email: `user${index + 1}@example.com`
    })
  );
};

export const createMockAnalytics = (overrides: Partial<Analytics> = {}): Analytics => ({
  totalUsers: 1000,
  activeUsers: 750,
  premiumUsers: 200,
  totalCredits: 50000,
  totalVideos: 2500,
  totalPhotos: 5000,
  monthlyGrowth: 15.5,
  conversionRate: 12.3,
  averageCreditsPerUser: 50,
  topContentTypes: [
    { type: 'video', count: 2500, percentage: 50 },
    { type: 'photo', count: 5000, percentage: 50 }
  ],
  userGrowthData: [
    { month: 'Jan', users: 800, premium: 150 },
    { month: 'Feb', users: 850, premium: 170 },
    { month: 'Mar', users: 900, premium: 180 },
    { month: 'Apr', users: 950, premium: 190 },
    { month: 'May', users: 1000, premium: 200 }
  ],
  ...overrides
});

/**
 * Test helpers for async operations
 */
export const waitFor = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

export const waitForElement = async (
  selector: string,
  timeout: number = 5000
): Promise<Element> => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    const element = document.querySelector(selector);
    if (element) {
      return element;
    }
    await waitFor(100);
  }
  
  throw new Error(`Element with selector "${selector}" not found within ${timeout}ms`);
};

/**
 * Mock API response helpers
 */
export const createMockApiResponse = <T>(data: T, success: boolean = true) => ({
  success,
  data: success ? data : null,
  error: success ? null : 'Mock error message',
  timestamp: new Date().toISOString()
});

export const createMockPaginatedResponse = <T>(
  items: T[],
  page: number = 1,
  pageSize: number = 10
) => ({
  items: items.slice((page - 1) * pageSize, page * pageSize),
  total: items.length,
  page,
  pageSize,
  totalPages: Math.ceil(items.length / pageSize),
  hasNext: page * pageSize < items.length,
  hasPrev: page > 1
});

/**
 * Component testing utilities
 */
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null
  });
  window.IntersectionObserver = mockIntersectionObserver;
};

export const mockResizeObserver = () => {
  const mockResizeObserver = jest.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null
  });
  window.ResizeObserver = mockResizeObserver;
};

/**
 * Local storage mocking
 */
export const mockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  const mockStorage = {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
    length: 0,
    key: jest.fn()
  };
  
  Object.defineProperty(window, 'localStorage', {
    value: mockStorage,
    writable: true
  });
  
  return mockStorage;
};

/**
 * Performance testing utilities
 */
export const measureRenderTime = async (renderFn: () => void): Promise<number> => {
  const startTime = performance.now();
  renderFn();
  await waitFor(0); // Wait for next tick
  const endTime = performance.now();
  return endTime - startTime;
};

export const measureMemoryUsage = (): number | null => {
  if ('memory' in performance) {
    return (performance as any).memory.usedJSHeapSize;
  }
  return null;
};

/**
 * Error testing utilities
 */
export const expectError = async (fn: () => Promise<any>, expectedError?: string) => {
  try {
    await fn();
    throw new Error('Expected function to throw an error');
  } catch (error) {
    if (expectedError && error instanceof Error) {
      expect(error.message).toContain(expectedError);
    }
  }
};

/**
 * Validation testing utilities
 */
export const testValidation = (
  validator: (data: any) => any,
  validData: any,
  invalidData: any[]
) => {
  // Test valid data
  expect(() => validator(validData)).not.toThrow();
  
  // Test invalid data
  invalidData.forEach(data => {
    expect(() => validator(data)).toThrow();
  });
};
