
import { useState } from 'react';
import { 
  Users, 
  Settings, 
  Home, 
  LogOut, 
  ChevronLeft,
  ChevronRight,
  Shield,
  Video,
  CreditCard,
  Activity,
  BarChart3,
  FileText,
  Bell,
  Database,
  Palette
} from 'lucide-react';
import { Button } from '@/interface/components/ui/button';

interface SidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  onLogout: () => void;
}

const Sidebar = ({ activeSection, onSectionChange, onLogout }: SidebarProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    { id: 'dashboard', label: 'Bảng điều khiển', icon: Home },
    { id: 'users', label: 'Quản lý người dùng', icon: Users },
    { id: 'credits', label: 'Quản lý Credits', icon: CreditCard },
    { id: 'content', label: 'Quản lý nội dung', icon: Video },
    { id: 'analytics', label: '<PERSON><PERSON><PERSON> c<PERSON>o & <PERSON>ân tích', icon: BarChart3 },
    { id: 'notifications', label: 'Thông báo', icon: Bell },
    { id: 'settings', label: 'Cài đặt hệ thống', icon: Settings },
  ];

  return (
    <div className={`bg-slate-900 text-white transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'} min-h-screen relative animate-slide-in`}>
      <div className="p-4">
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Shield className="w-6 h-6 text-white" />
          </div>
          {!isCollapsed && (
            <div>
              <h2 className="text-xl font-bold">SaaS Admin</h2>
              <p className="text-slate-400 text-sm">Platform Video & Photo Cá nhân</p>
            </div>
          )}
        </div>

        <nav className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 hover:bg-slate-800 ${
                  activeSection === item.id ? 'bg-indigo-600 hover:bg-indigo-700' : ''
                }`}
                title={isCollapsed ? item.label : ''}
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                {!isCollapsed && <span className="text-sm font-medium">{item.label}</span>}
              </button>
            );
          })}
        </nav>

        {/* Quick Stats in Sidebar */}
        {!isCollapsed && (
          <div className="mt-8 p-3 bg-slate-800 rounded-lg">
            <h4 className="text-sm font-medium text-slate-300 mb-3">Thống kê cá nhân hôm nay</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Video className="w-3 h-3 text-blue-400" />
                  <span className="text-xs text-slate-400">Videos cá nhân</span>
                </div>
                <span className="text-xs font-medium text-white">342</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CreditCard className="w-3 h-3 text-purple-400" />
                  <span className="text-xs text-slate-400">Credits sử dụng</span>
                </div>
                <span className="text-xs font-medium text-white">456</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Activity className="w-3 h-3 text-orange-400" />
                  <span className="text-xs text-slate-400">Người dùng active</span>
                </div>
                <span className="text-xs font-medium text-white">89</span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="absolute bottom-4 left-4 right-4">
        <Button
          onClick={onLogout}
          variant="ghost"
          className={`w-full justify-start text-slate-400 hover:text-white hover:bg-slate-800 ${isCollapsed ? 'px-3' : ''}`}
          title={isCollapsed ? 'Đăng xuất' : ''}
        >
          <LogOut className="w-5 h-5 flex-shrink-0" />
          {!isCollapsed && <span className="ml-3">Đăng xuất</span>}
        </Button>
      </div>

      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -right-3 top-20 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full p-1.5 shadow-lg transition-colors duration-200"
      >
        {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
      </button>
    </div>
  );
};

export default Sidebar;
