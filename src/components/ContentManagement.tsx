
import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { But<PERSON> } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { 
  Video,
  Image,
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Download,
  Trash2,
  Eye,
  Clock,
  User,
  Calendar
} from 'lucide-react';

const ContentManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [contentFilter, setContentFilter] = useState('all');
  const [selectedItems, setSelectedItems] = useState<number[]>([]);

  const contentData = [
    {
      id: 1,
      type: 'video',
      title: 'Video marketing sản phẩm',
      thumbnail: '/placeholder.svg',
      creator: 'Nguyễn Văn A',
      duration: '00:02:45',
      size: '45.2 MB',
      createdAt: '2024-01-15 10:30',
      status: 'completed',
      views: 245,
      creditsUsed: 50
    },
    {
      id: 2,
      type: 'photo',
      title: 'Ảnh profile AI',
      thumbnail: '/placeholder.svg',
      creator: '<PERSON>rầ<PERSON> Thị B',
      duration: null,
      size: '2.8 MB',
      createdAt: '2024-01-15 09:15',
      status: 'completed',
      views: 89,
      creditsUsed: 15
    },
    {
      id: 3,
      type: 'video',
      title: 'Video giới thiệu doanh nghiệp',
      thumbnail: '/placeholder.svg',
      creator: 'Lê Văn C',
      duration: '00:01:30',
      size: '28.7 MB',
      createdAt: '2024-01-15 08:45',
      status: 'processing',
      views: 0,
      creditsUsed: 35
    },
    {
      id: 4,
      type: 'photo',
      title: 'Logo design AI',
      thumbnail: '/placeholder.svg',
      creator: 'Phạm Thị D',
      duration: null,
      size: '1.2 MB',
      createdAt: '2024-01-14 16:20',
      status: 'completed',
      views: 156,
      creditsUsed: 20
    }
  ];

  const filteredContent = contentData.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.creator.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = contentFilter === 'all' || item.type === contentFilter;
    return matchesSearch && matchesFilter;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Hoàn thành';
      case 'processing': return 'Đang xử lý';
      case 'failed': return 'Thất bại';
      default: return 'Không xác định';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Quản lý nội dung</h2>
          <p className="text-slate-600">Quản lý tất cả videos và photos được tạo</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Xuất danh sách
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Video className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Tổng Videos</p>
              <p className="text-2xl font-bold text-slate-900">
                {contentData.filter(item => item.type === 'video').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Image className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Tổng Photos</p>
              <p className="text-2xl font-bold text-slate-900">
                {contentData.filter(item => item.type === 'photo').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Eye className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Tổng lượt xem</p>
              <p className="text-2xl font-bold text-slate-900">
                {contentData.reduce((sum, item) => sum + item.views, 0)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Đang xử lý</p>
              <p className="text-2xl font-bold text-slate-900">
                {contentData.filter(item => item.status === 'processing').length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <Input
              placeholder="Tìm kiếm theo tiêu đề hoặc người tạo..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <select 
            value={contentFilter} 
            onChange={(e) => setContentFilter(e.target.value)}
            className="px-3 py-2 border border-slate-200 rounded-lg"
          >
            <option value="all">Tất cả</option>
            <option value="video">Videos</option>
            <option value="photo">Photos</option>
          </select>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Lọc nâng cao
          </Button>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredContent.map((item) => (
            <div key={item.id} className="bg-white border border-slate-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <img 
                  src={item.thumbnail} 
                  alt={item.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-2 left-2">
                  {item.type === 'video' ? (
                    <Badge className="bg-blue-600 text-white">
                      <Video className="w-3 h-3 mr-1" />
                      Video
                    </Badge>
                  ) : (
                    <Badge className="bg-green-600 text-white">
                      <Image className="w-3 h-3 mr-1" />
                      Photo
                    </Badge>
                  )}
                </div>
                <div className="absolute top-2 right-2">
                  <Badge className={getStatusColor(item.status)}>
                    {getStatusText(item.status)}
                  </Badge>
                </div>
                {item.type === 'video' && (
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
                    {item.duration}
                  </div>
                )}
              </div>
              
              <div className="p-4">
                <h3 className="font-medium text-slate-900 mb-2 line-clamp-2">{item.title}</h3>
                
                <div className="flex items-center space-x-2 text-sm text-slate-500 mb-3">
                  <User className="w-3 h-3" />
                  <span>{item.creator}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm text-slate-500 mb-3">
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-3 h-3" />
                    <span>{item.createdAt}</span>
                  </div>
                  <span>{item.size}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Eye className="w-3 h-3 text-slate-400" />
                      <span>{item.views}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {item.creditsUsed} credits
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="ghost">
                      <Eye className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <Download className="w-3 h-3" />
                    </Button>
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default ContentManagement;
