
import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { But<PERSON> } from '@/interface/components/ui/button';
import { Badge } from '@/interface/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  CreditCard,
  Video,
  Download,
  Calendar,
  Filter,
  Eye
} from 'lucide-react';
import { BarChart, Bar, LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

const Analytics = () => {
  const [dateRange, setDateRange] = useState('7days');

  const revenueData = [
    { name: 'T2', revenue: 2400, users: 120 },
    { name: 'T3', revenue: 1398, users: 98 },
    { name: 'T4', revenue: 9800, users: 180 },
    { name: 'T5', revenue: 3908, users: 150 },
    { name: 'T6', revenue: 4800, users: 200 },
    { name: 'T7', revenue: 3800, users: 170 },
    { name: 'CN', revenue: 4300, users: 190 }
  ];

  const usageData = [
    { name: 'Video Portrait', value: 45, color: '#8884d8' },
    { name: 'Video Landscape', value: 30, color: '#82ca9d' },
    { name: 'Photo Enhancement', value: 15, color: '#ffc658' },
    { name: 'Photo Generation', value: 10, color: '#ff7300' }
  ];

  const topUsers = [
    { name: 'Nguyễn Văn A', credits: 1240, videos: 45, revenue: 580000 },
    { name: 'Trần Thị B', credits: 980, videos: 38, revenue: 460000 },
    { name: 'Lê Văn C', credits: 856, videos: 32, revenue: 420000 },
    { name: 'Phạm Thị D', credits: 720, videos: 28, revenue: 380000 },
    { name: 'Hoàng Văn E', credits: 650, videos: 25, revenue: 340000 }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Báo cáo & Phân tích</h2>
          <p className="text-slate-600">Thống kê chi tiết về hiệu suất platform</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={dateRange} 
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-slate-200 rounded-lg"
          >
            <option value="7days">7 ngày qua</option>
            <option value="30days">30 ngày qua</option>
            <option value="90days">3 tháng qua</option>
          </select>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Xuất báo cáo
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Doanh thu</p>
              <p className="text-2xl font-bold text-slate-900">28.4M VNĐ</p>
              <p className="text-sm text-green-600">+12.5% từ tuần trước</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Người dùng hoạt động</p>
              <p className="text-2xl font-bold text-slate-900">1,847</p>
              <p className="text-sm text-blue-600">+8.2% từ tuần trước</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Credits tiêu thụ</p>
              <p className="text-2xl font-bold text-slate-900">45,672</p>
              <p className="text-sm text-purple-600">+15.8% từ tuần trước</p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Video className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Nội dung tạo ra</p>
              <p className="text-2xl font-bold text-slate-900">2,394</p>
              <p className="text-sm text-orange-600">+22.1% từ tuần trước</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Doanh thu theo ngày</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={revenueData}>
              <defs>
                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="name" stroke="#64748b" />
              <YAxis stroke="#64748b" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'white', 
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px'
                }} 
              />
              <Area type="monotone" dataKey="revenue" stroke="#10b981" fillOpacity={1} fill="url(#colorRevenue)" />
            </AreaChart>
          </ResponsiveContainer>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Phân bố loại nội dung</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={usageData}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {usageData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Top Users */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Top người dùng theo Credits</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-200">
                <th className="text-left py-3 px-4 font-medium text-slate-700">Người dùng</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Credits đã dùng</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Videos tạo</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Doanh thu</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Hạng</th>
              </tr>
            </thead>
            <tbody>
              {topUsers.map((user, index) => (
                <tr key={index} className="border-b border-slate-100 hover:bg-slate-50">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {user.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                        </span>
                      </div>
                      <span className="font-medium text-slate-900">{user.name}</span>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <Badge className="bg-purple-100 text-purple-800">
                      {user.credits.toLocaleString()}
                    </Badge>
                  </td>
                  <td className="py-4 px-4 text-slate-600">{user.videos}</td>
                  <td className="py-4 px-4 text-slate-600">{user.revenue.toLocaleString()} VNĐ</td>
                  <td className="py-4 px-4">
                    <Badge variant="outline">#{index + 1}</Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;
