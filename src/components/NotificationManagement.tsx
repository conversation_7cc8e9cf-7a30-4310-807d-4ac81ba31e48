import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { Button } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { Switch } from '@/interface/components/ui/switch';
import { 
  Bell,
  Mail,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  Clock,
  Send,
  Users,
  Plus,
  Search,
  Filter,
  MoreHorizontal
} from 'lucide-react';

const NotificationManagement = () => {
  const [activeTab, setActiveTab] = useState('notifications');
  const [searchTerm, setSearchTerm] = useState('');

  const notifications = [
    {
      id: 1,
      type: 'system',
      title: '<PERSON>ệ thống bảo trì',
      message: '<PERSON>ệ thống sẽ bảo trì từ 2:00 - 4:00 sáng ngày mai',
      priority: 'high',
      status: 'sent',
      recipients: 1247,
      createdAt: '2024-01-15 10:30',
      icon: <PERSON><PERSON><PERSON>rian<PERSON>,
      color: 'text-red-600'
    },
    {
      id: 2,
      type: 'feature',
      title: 'T<PERSON>h năng mới: AI Video Enhancement',
      message: '<PERSON>úng tôi vừa ra mắt tính năng nâng cao chất lượng video bằng AI',
      priority: 'medium',
      status: 'sent',
      recipients: 1247,
      createdAt: '2024-01-15 09:15',
      icon: CheckCircle,
      color: 'text-blue-600'
    },
    {
      id: 3,
      type: 'promotion',
      title: 'Khuyến mãi Credits tháng 1',
      message: 'Nhận thêm 20% credits khi nạp từ 500k trở lên',
      priority: 'medium',
      status: 'draft',
      recipients: 0,
      createdAt: '2024-01-14 16:45',
      icon: Bell,
      color: 'text-green-600'
    }
  ];

  const templates = [
    {
      id: 1,
      name: 'Chào mừng người dùng mới',
      subject: 'Chào mừng bạn đến với platform',
      category: 'welcome'
    },
    {
      id: 2,
      name: 'Thông báo bảo trì',
      subject: 'Thông báo bảo trì hệ thống',
      category: 'system'
    },
    {
      id: 3,
      name: 'Khuyến mãi đặc biệt',
      subject: 'Ưu đãi đặc biệt dành cho bạn',
      category: 'promotion'
    }
  ];

  const settings = {
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    autoSendWelcome: true,
    autoSendMaintenance: true
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Quản lý thông báo</h2>
          <p className="text-slate-600">Gửi và quản lý thông báo đến người dùng</p>
        </div>
        <Button className="bg-indigo-600 hover:bg-indigo-700">
          <Plus className="w-4 h-4 mr-2" />
          Tạo thông báo mới
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b border-slate-200">
        <button
          onClick={() => setActiveTab('notifications')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'notifications' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Thông báo
        </button>
        <button
          onClick={() => setActiveTab('templates')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'templates' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Mẫu thông báo
        </button>
        <button
          onClick={() => setActiveTab('settings')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'settings' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Cài đặt
        </button>
      </div>

      {activeTab === 'notifications' && (
        <>
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Send className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Đã gửi hôm nay</p>
                  <p className="text-2xl font-bold text-slate-900">1,247</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Tỷ lệ mở</p>
                  <p className="text-2xl font-bold text-slate-900">78.5%</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Người nhận</p>
                  <p className="text-2xl font-bold text-slate-900">2,494</p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Clock className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Đang chờ gửi</p>
                  <p className="text-2xl font-bold text-slate-900">5</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Notifications List */}
          <Card className="p-6">
            <div className="flex items-center space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Tìm kiếm thông báo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline">
                <Filter className="w-4 h-4 mr-2" />
                Lọc
              </Button>
            </div>

            <div className="space-y-4">
              {notifications.map((notification) => {
                const Icon = notification.icon;
                return (
                  <div key={notification.id} className="flex items-start space-x-4 p-4 border border-slate-200 rounded-lg hover:bg-slate-50">
                    <div className="w-10 h-10 bg-slate-100 rounded-lg flex items-center justify-center">
                      <Icon className={`w-5 h-5 ${notification.color}`} />
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-medium text-slate-900">{notification.title}</h3>
                          <p className="text-sm text-slate-600 mt-1">{notification.message}</p>
                          <div className="flex items-center space-x-4 mt-2 text-sm text-slate-500">
                            <span>Gửi lúc: {notification.createdAt}</span>
                            <span>Người nhận: {notification.recipients}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Badge className={getPriorityColor(notification.priority)}>
                            {notification.priority === 'high' ? 'Cao' : 
                             notification.priority === 'medium' ? 'Trung bình' : 'Thấp'}
                          </Badge>
                          <Badge className={getStatusColor(notification.status)}>
                            {notification.status === 'sent' ? 'Đã gửi' : 
                             notification.status === 'draft' ? 'Nháp' : 'Đã lên lịch'}
                          </Badge>
                          <Button size="sm" variant="ghost">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </Card>
        </>
      )}

      {activeTab === 'templates' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-slate-900">Mẫu thông báo</h3>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Tạo mẫu mới
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map((template) => (
              <div key={template.id} className="border border-slate-200 rounded-lg p-4 hover:shadow-sm">
                <h4 className="font-medium text-slate-900 mb-2">{template.name}</h4>
                <p className="text-sm text-slate-600 mb-3">{template.subject}</p>
                <div className="flex items-center justify-between">
                  <Badge variant="outline">{template.category}</Badge>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">Sửa</Button>
                    <Button size="sm">Sử dụng</Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {activeTab === 'settings' && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-slate-900 mb-6">Cài đặt thông báo</h3>
          
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-slate-700">Email thông báo</p>
                <p className="text-sm text-slate-500">Gửi thông báo qua email</p>
              </div>
              <Switch defaultChecked={settings.emailNotifications} />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-slate-700">Push notifications</p>
                <p className="text-sm text-slate-500">Gửi thông báo đẩy trên trình duyệt</p>
              </div>
              <Switch defaultChecked={settings.pushNotifications} />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-slate-700">SMS thông báo</p>
                <p className="text-sm text-slate-500">Gửi thông báo qua SMS</p>
              </div>
              <Switch defaultChecked={settings.smsNotifications} />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-slate-700">Tự động chào mừng</p>
                <p className="text-sm text-slate-500">Tự động gửi email chào mừng người dùng mới</p>
              </div>
              <Switch defaultChecked={settings.autoSendWelcome} />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-slate-700">Tự động thông báo bảo trì</p>
                <p className="text-sm text-slate-500">Tự động thông báo khi có bảo trì hệ thống</p>
              </div>
              <Switch defaultChecked={settings.autoSendMaintenance} />
            </div>
          </div>

          <div className="mt-8">
            <Button className="bg-indigo-600 hover:bg-indigo-700">
              Lưu cài đặt
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};

export default NotificationManagement;
