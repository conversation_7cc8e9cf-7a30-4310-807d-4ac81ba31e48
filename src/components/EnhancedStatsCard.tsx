
import { LucideIcon } from 'lucide-react';
import { Card } from '@/interface/components/ui/card';

interface EnhancedStatsCardProps {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: LucideIcon;
  gradient: string;
  subtitle?: string;
  trend?: number[];
}

const EnhancedStatsCard = ({ 
  title, 
  value, 
  change, 
  changeType, 
  icon: Icon, 
  gradient,
  subtitle,
  trend 
}: EnhancedStatsCardProps) => {
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      case 'neutral': return 'text-slate-600';
      default: return 'text-slate-600';
    }
  };

  return (
    <Card className="p-6 bg-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-fade-in relative overflow-hidden">
      {/* Gradient background accent */}
      <div className={`absolute top-0 right-0 w-32 h-32 ${gradient} opacity-10 rounded-full -mr-16 -mt-16`}></div>
      
      <div className="flex items-start justify-between relative z-10">
        <div className="flex-1">
          <p className="text-slate-600 text-sm font-medium mb-1">{title}</p>
          <p className="text-3xl font-bold text-slate-900 mb-2">{value}</p>
          {subtitle && (
            <p className="text-xs text-slate-500 mb-2">{subtitle}</p>
          )}
          <div className="flex items-center space-x-2">
            <span className={`text-sm font-medium ${getChangeColor()}`}>
              {change}
            </span>
            <span className="text-slate-500 text-sm">so với tháng trước</span>
          </div>
        </div>
        
        <div className={`w-12 h-12 rounded-lg ${gradient} flex items-center justify-center shadow-lg`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>

      {/* Mini trend chart */}
      {trend && (
        <div className="mt-4 relative z-10">
          <div className="flex items-end space-x-1 h-8">
            {trend.map((value, index) => (
              <div
                key={index}
                className={`bg-gradient-to-t ${gradient} opacity-70 rounded-sm flex-1`}
                style={{ height: `${(value / Math.max(...trend)) * 100}%` }}
              ></div>
            ))}
          </div>
        </div>
      )}
    </Card>
  );
};

export default EnhancedStatsCard;
