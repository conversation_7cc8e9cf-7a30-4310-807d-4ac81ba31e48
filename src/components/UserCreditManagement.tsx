import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { But<PERSON> } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  UserPlus, 
  Mail, 
  Phone,
  CreditCard,
  Video,
  Image,
  Plus,
  Minus,
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Users,
  Activity
} from 'lucide-react';

const UserCreditManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [creditAmount, setCreditAmount] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedUserDetails, setSelectedUserDetails] = useState<number | null>(null);
  
  const users = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: 'ng<PERSON><PERSON><PERSON>@email.com',
      phone: '+84 123 456 789',
      credits: 150,
      videosCreated: 25,
      photosCreated: 80,
      totalUsed: 450,
      totalAdded: 600,
      monthlyStats: {
        videosCompleted: 12,
        videosErrors: 2,
        photosCompleted: 45,
        photosErrors: 3,
        creditsUsed: 180,
        creditsAdded: 200
      },
      lastActivity: '2024-01-15 14:30',
      joinDate: '2023-06-15',
      status: 'active'
    },
    {
      id: 2,
      name: 'Trần Thị B',
      email: '<EMAIL>',
      phone: '+84 987 654 321',
      credits: 50,
      videosCreated: 8,
      photosCreated: 30,
      totalUsed: 200,
      totalAdded: 250,
      monthlyStats: {
        videosCompleted: 5,
        videosErrors: 1,
        photosCompleted: 20,
        photosErrors: 2,
        creditsUsed: 75,
        creditsAdded: 100
      },
      lastActivity: '2024-01-14 09:15',
      joinDate: '2023-08-20',
      status: 'active'
    },
    {
      id: 3,
      name: 'Lê Văn C',
      email: '<EMAIL>',
      phone: '+84 555 666 777',
      credits: 200,
      videosCreated: 45,
      photosCreated: 120,
      totalUsed: 800,
      totalAdded: 1000,
      monthlyStats: {
        videosCompleted: 18,
        videosErrors: 3,
        photosCompleted: 65,
        photosErrors: 1,
        creditsUsed: 280,
        creditsAdded: 300
      },
      lastActivity: '2024-01-15 16:45',
      joinDate: '2023-03-10',
      status: 'premium'
    }
  ];

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddCredits = (userId: number, amount: number) => {
    console.log(`Adding ${amount} credits to user ${userId}`);
  };

  const handleSubtractCredits = (userId: number, amount: number) => {
    console.log(`Subtracting ${amount} credits from user ${userId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Hoạt động';
      case 'premium': return 'Premium';
      case 'inactive': return 'Không hoạt động';
      default: return 'Không xác định';
    }
  };

  const renderUserDetails = (user: any) => {
    const successRate = {
      video: Math.round((user.monthlyStats.videosCompleted / (user.monthlyStats.videosCompleted + user.monthlyStats.videosErrors)) * 100),
      photo: Math.round((user.monthlyStats.photosCompleted / (user.monthlyStats.photosCompleted + user.monthlyStats.photosErrors)) * 100)
    };

    return (
      <div className="mt-6 p-4 sm:p-6 bg-slate-50 rounded-lg">
        <h4 className="text-lg font-semibold mb-4">Chi tiết tháng này - {user.name}</h4>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Videos hoàn thành</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.videosCompleted}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Videos lỗi</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.videosErrors}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Image className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Photos hoàn thành</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.photosCompleted}</p>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-slate-600">Photos lỗi</p>
                <p className="text-xl font-bold text-slate-900">{user.monthlyStats.photosErrors}</p>
              </div>
            </div>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-4">
            <h5 className="font-semibold mb-3">Tỷ lệ thành công</h5>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Videos</span>
                  <span className="text-sm font-medium">{successRate.video}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${successRate.video}%` }}
                  ></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Photos</span>
                  <span className="text-sm font-medium">{successRate.photo}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${successRate.photo}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h5 className="font-semibold mb-3">Credits tháng này</h5>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Đã sử dụng</span>
                <span className="font-medium text-red-600">{user.monthlyStats.creditsUsed}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-slate-600">Đã thêm</span>
                <span className="font-medium text-green-600">{user.monthlyStats.creditsAdded}</span>
              </div>
              <div className="flex justify-between items-center pt-2 border-t">
                <span className="text-sm font-medium">Chênh lệch</span>
                <span className={`font-bold ${user.monthlyStats.creditsAdded - user.monthlyStats.creditsUsed >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {user.monthlyStats.creditsAdded - user.monthlyStats.creditsUsed > 0 ? '+' : ''}{user.monthlyStats.creditsAdded - user.monthlyStats.creditsUsed}
                </span>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  };

  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Quản lý người dùng & Credits</h2>
          <p className="text-sm sm:text-base text-slate-600">Quản lý người dùng, credits và thống kê chi tiết theo tháng</p>
        </div>
        <Button className="bg-indigo-600 hover:bg-indigo-700 flex-shrink-0">
          <UserPlus className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Thêm người dùng</span>
          <span className="sm:hidden">Thêm</span>
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b border-slate-200 overflow-x-auto">
        <button
          onClick={() => setActiveTab('overview')}
          className={`pb-2 px-1 border-b-2 transition-colors whitespace-nowrap ${
            activeTab === 'overview' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Tổng quan
        </button>
        <button
          onClick={() => setActiveTab('detailed')}
          className={`pb-2 px-1 border-b-2 transition-colors whitespace-nowrap ${
            activeTab === 'detailed' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Chi tiết theo tháng
        </button>
      </div>

      {activeTab === 'overview' && (
        <>
          {/* Stats Overview */}
          <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Tổng Credits hiện tại</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.credits, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Plus className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Credits đã thêm</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.totalAdded, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <Minus className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Credits đã sử dụng</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.totalUsed, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Video className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Tổng Videos</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.videosCreated, 0)}
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* User Management Table */}
          <Card className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Tìm kiếm theo tên hoặc email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" className="flex-shrink-0">
                <Filter className="w-4 h-4 mr-2" />
                Lọc
              </Button>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full min-w-[800px]">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Người dùng</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Credits</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Nội dung</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Trạng thái</th>
                    <th className="text-left py-3 px-2 sm:px-4 font-medium text-slate-700">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="py-4 px-2 sm:px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-white font-medium text-xs sm:text-sm">
                              {user.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                            </span>
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-slate-900 truncate">{user.name}</p>
                            <div className="flex items-center text-xs sm:text-sm text-slate-500">
                              <Mail className="w-3 h-3 mr-1 flex-shrink-0" />
                              <span className="truncate">{user.email}</span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <Badge className="bg-blue-100 text-blue-800 text-xs">
                          {user.credits.toLocaleString()}
                        </Badge>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <div className="space-y-1">
                          <div className="flex items-center text-xs sm:text-sm">
                            <Video className="w-3 h-3 mr-1 text-blue-600 flex-shrink-0" />
                            <span>{user.videosCreated}</span>
                          </div>
                          <div className="flex items-center text-xs sm:text-sm">
                            <Image className="w-3 h-3 mr-1 text-green-600 flex-shrink-0" />
                            <span>{user.photosCreated}</span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <Badge className={getStatusColor(user.status)}>
                          {getStatusText(user.status)}
                        </Badge>
                      </td>
                      <td className="py-4 px-2 sm:px-4">
                        <div className="flex items-center space-x-2">
                          {selectedUser === user.id ? (
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                placeholder="Credits"
                                value={creditAmount}
                                onChange={(e) => setCreditAmount(e.target.value)}
                                className="w-16 sm:w-20 text-xs"
                              />
                              <Button
                                size="sm"
                                onClick={() => {
                                  handleAddCredits(user.id, parseInt(creditAmount));
                                  setSelectedUser(null);
                                  setCreditAmount('');
                                }}
                                className="bg-green-600 hover:bg-green-700 px-2"
                              >
                                <Plus className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedUser(null);
                                  setCreditAmount('');
                                }}
                                className="px-2"
                              >
                                Hủy
                              </Button>
                            </div>
                          ) : (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => setSelectedUser(user.id)}
                                className="text-xs px-2"
                              >
                                <CreditCard className="w-3 h-3 mr-1" />
                                <span className="hidden sm:inline">Credits</span>
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => setSelectedUserDetails(selectedUserDetails === user.id ? null : user.id)}
                                className="px-2"
                              >
                                <BarChart3 className="w-3 h-3" />
                              </Button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* User Details */}
            {selectedUserDetails && (
              <>
                {filteredUsers.filter(user => user.id === selectedUserDetails).map(user => (
                  <div key={user.id}>
                    {renderUserDetails(user)}
                  </div>
                ))}
              </>
            )}
          </Card>
        </>
      )}

      {activeTab === 'detailed' && (
        <div className="spacing-responsive">
          <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
            {filteredUsers.map((user) => (
              <Card key={user.id} className="p-4 sm:p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-sm">
                      {user.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                    </span>
                  </div>
                  <div className="min-w-0 flex-1">
                    <h3 className="font-semibold text-slate-900 truncate">{user.name}</h3>
                    <Badge className={getStatusColor(user.status)}>
                      {getStatusText(user.status)}
                    </Badge>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-slate-700 mb-2">Thống kê tháng này</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Videos hoàn thành</span>
                        <span className="font-medium text-green-600">{user.monthlyStats.videosCompleted}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Videos lỗi</span>
                        <span className="font-medium text-red-600">{user.monthlyStats.videosErrors}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Photos hoàn thành</span>
                        <span className="font-medium text-green-600">{user.monthlyStats.photosCompleted}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Photos lỗi</span>
                        <span className="font-medium text-red-600">{user.monthlyStats.photosErrors}</span>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex justify-between mb-2">
                      <span className="text-sm text-slate-600">Credits đã dùng</span>
                      <span className="font-medium text-red-600">{user.monthlyStats.creditsUsed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-600">Credits đã thêm</span>
                      <span className="font-medium text-green-600">{user.monthlyStats.creditsAdded}</span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default UserCreditManagement;
