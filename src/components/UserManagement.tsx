
import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { But<PERSON> } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  UserPlus, 
  Mail, 
  Phone,
  CreditCard,
  Video,
  Image,
  Plus,
  Minus
} from 'lucide-react';

const UserManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [creditAmount, setCreditAmount] = useState('');
  
  const users = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+84 123 456 789',
      credits: 150,
      videosCreated: 25,
      photosCreated: 80,
      totalUsed: 450,
      totalAdded: 600
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+84 987 654 321',
      credits: 50,
      videosCreated: 8,
      photosCreated: 30,
      totalUsed: 200,
      totalAdded: 250
    },
    {
      id: 3,
      name: '<PERSON><PERSON>n C',
      email: '<EMAIL>',
      phone: '+84 555 666 777',
      credits: 200,
      videosCreated: 45,
      photosCreated: 120,
      totalUsed: 800,
      totalAdded: 1000
    }
  ];

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddCredits = (userId: number, amount: number) => {
    console.log(`Adding ${amount} credits to user ${userId}`);
    // Implement add credits logic
  };

  const handleSubtractCredits = (userId: number, amount: number) => {
    console.log(`Subtracting ${amount} credits from user ${userId}`);
    // Implement subtract credits logic
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-slate-900">Quản lý người dùng & Credits</h2>
          <p className="text-slate-600">Quản lý người dùng và credits cá nhân</p>
        </div>
        <Button className="bg-indigo-600 hover:bg-indigo-700">
          <UserPlus className="w-4 h-4 mr-2" />
          Thêm người dùng
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CreditCard className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Tổng Credits hiện tại</p>
              <p className="text-2xl font-bold text-slate-900">
                {users.reduce((sum, user) => sum + user.credits, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Plus className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Credits đã thêm</p>
              <p className="text-2xl font-bold text-slate-900">
                {users.reduce((sum, user) => sum + user.totalAdded, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Minus className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Credits đã sử dụng</p>
              <p className="text-2xl font-bold text-slate-900">
                {users.reduce((sum, user) => sum + user.totalUsed, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Video className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-slate-600">Tổng Videos</p>
              <p className="text-2xl font-bold text-slate-900">
                {users.reduce((sum, user) => sum + user.videosCreated, 0)}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <Card className="p-6">
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <Input
              placeholder="Tìm kiếm theo tên hoặc email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Lọc
          </Button>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-200">
                <th className="text-left py-3 px-4 font-medium text-slate-700">Người dùng</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Credits</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Nội dung</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Thống kê Credits</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.map((user) => (
                <tr key={user.id} className="border-b border-slate-100 hover:bg-slate-50">
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-medium">
                          {user.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-slate-900">{user.name}</p>
                        <div className="flex items-center text-sm text-slate-500">
                          <Mail className="w-3 h-3 mr-1" />
                          {user.email}
                        </div>
                        <div className="flex items-center text-sm text-slate-500">
                          <Phone className="w-3 h-3 mr-1" />
                          {user.phone}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <Badge className="bg-blue-100 text-blue-800">
                      {user.credits.toLocaleString()} credits
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <div className="space-y-1">
                      <div className="flex items-center text-sm">
                        <Video className="w-3 h-3 mr-1 text-blue-600" />
                        <span>{user.videosCreated} videos</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <Image className="w-3 h-3 mr-1 text-green-600" />
                        <span>{user.photosCreated} photos</span>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="text-sm space-y-1">
                      <div className="text-green-600">Đã thêm: {user.totalAdded.toLocaleString()}</div>
                      <div className="text-red-600">Đã dùng: {user.totalUsed.toLocaleString()}</div>
                    </div>
                  </td>
                  <td className="py-4 px-4">
                    <div className="flex items-center space-x-2">
                      {selectedUser === user.id ? (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="number"
                            placeholder="Số credits"
                            value={creditAmount}
                            onChange={(e) => setCreditAmount(e.target.value)}
                            className="w-24"
                          />
                          <Button
                            size="sm"
                            onClick={() => {
                              handleAddCredits(user.id, parseInt(creditAmount));
                              setSelectedUser(null);
                              setCreditAmount('');
                            }}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <Plus className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              handleSubtractCredits(user.id, parseInt(creditAmount));
                              setSelectedUser(null);
                              setCreditAmount('');
                            }}
                            className="border-red-200 text-red-600 hover:bg-red-50"
                          >
                            <Minus className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSelectedUser(null);
                              setCreditAmount('');
                            }}
                          >
                            Hủy
                          </Button>
                        </div>
                      ) : (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setSelectedUser(user.id)}
                          >
                            <CreditCard className="w-3 h-3 mr-1" />
                            Credits
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default UserManagement;
