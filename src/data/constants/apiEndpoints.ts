// /**
//  * API endpoint definitions and configurations
//  */

// const API_BASE_URL = process.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// export const API_ENDPOINTS = {
//   // User endpoints
//   USERS: {
//     LIST: `${API_BASE_URL}/users`,
//     GET: (id: number) => `${API_BASE_URL}/users/${id}`,
//     CREATE: `${API_BASE_URL}/users`,
//     UPDATE: (id: number) => `${API_BASE_URL}/users/${id}`,
//     DELETE: (id: number) => `${API_BASE_URL}/users/${id}`,
//     STATS: `${API_BASE_URL}/users/stats`,
//     ACTIVITY: (id: number) => `${API_BASE_URL}/users/${id}/activity`
//   },

//   // Credit endpoints
//   CREDITS: {
//     LIST: `${API_BASE_URL}/credits`,
//     TRANSACTIONS: `${API_BASE_URL}/credits/transactions`,
//     ADD: `${API_BASE_URL}/credits/add`,
//     SUBTRACT: `${API_BASE_URL}/credits/subtract`,
//     HISTORY: (userId: number) => `${API_BASE_URL}/credits/history/${userId}`,
//     SUMMARY: `${API_BASE_URL}/credits/summary`
//   },

//   // Content endpoints
//   CONTENT: {
//     LIST: `${API_BASE_URL}/content`,
//     GET: (id: string) => `${API_BASE_URL}/content/${id}`,
//     CREATE: `${API_BASE_URL}/content`,
//     UPDATE: (id: string) => `${API_BASE_URL}/content/${id}`,
//     DELETE: (id: string) => `${API_BASE_URL}/content/${id}`,
//     BULK_ACTION: `${API_BASE_URL}/content/bulk`,
//     STATS: `${API_BASE_URL}/content/stats`
//   },

//   // Analytics endpoints
//   ANALYTICS: {
//     DASHBOARD: `${API_BASE_URL}/analytics/dashboard`,
//     USERS: `${API_BASE_URL}/analytics/users`,
//     CONTENT: `${API_BASE_URL}/analytics/content`,
//     CREDITS: `${API_BASE_URL}/analytics/credits`,
//     REPORTS: `${API_BASE_URL}/analytics/reports`,
//     EXPORT: `${API_BASE_URL}/analytics/export`
//   },

//   // Notification endpoints
//   NOTIFICATIONS: {
//     LIST: `${API_BASE_URL}/notifications`,
//     GET: (id: string) => `${API_BASE_URL}/notifications/${id}`,
//     CREATE: `${API_BASE_URL}/notifications`,
//     UPDATE: (id: string) => `${API_BASE_URL}/notifications/${id}`,
//     DELETE: (id: string) => `${API_BASE_URL}/notifications/${id}`,
//     SEND: (id: string) => `${API_BASE_URL}/notifications/${id}/send`,
//     TEMPLATES: `${API_BASE_URL}/notifications/templates`
//   },

//   // System endpoints
//   SYSTEM: {
//     SETTINGS: `${API_BASE_URL}/system/settings`,
//     HEALTH: `${API_BASE_URL}/system/health`,
//     AUDIT_LOGS: `${API_BASE_URL}/system/audit-logs`,
//     BACKUPS: `${API_BASE_URL}/system/backups`,
//     FEATURE_FLAGS: `${API_BASE_URL}/system/feature-flags`
//   },

//   // Auth endpoints
//   AUTH: {
//     LOGIN: `${API_BASE_URL}/auth/login`,
//     LOGOUT: `${API_BASE_URL}/auth/logout`,
//     REFRESH: `${API_BASE_URL}/auth/refresh`,
//     PROFILE: `${API_BASE_URL}/auth/profile`
//   }
// } as const;

// export const API_CONFIG = {
//   TIMEOUT: 30000, // 30 seconds
//   RETRY_ATTEMPTS: 3,
//   RETRY_DELAY: 1000, // 1 second
//   HEADERS: {
//     'Content-Type': 'application/json',
//     'Accept': 'application/json'
//   }
// } as const;
